

 
 touchmain.html 中需要接入mqtt：
 1. run-status-panel 三个控制按钮对应的参考 /debug1/设备操作.html
 2. <!-- 系统运行状态 - 5种状态显示 -->、<!-- 网侧负载无功电流图表 -->、<!-- 系统关键参数 - 2-3列网格布局 --> 调用 main.js MQTTElectricalDataManager 类相关方法刷新数据。
 
3.  相关mqtt状态反应在 footer-status-info 中。

 


# todo
优化parameter-config.js中
1. 把MQTT连接相关统一为一个方法

目前有3处 username: 'FastBee',


老password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.g1HCISIvQd6YkNgWhblKnXqHeRI74lnP1F8qZOd9XV5a7J41Qi77f9jLxxWd_EVN0XJPP1haYeRK3Uz_xrbEuA',


新password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.1O79OeRWpHjRa5nps9L9ETtplMTLmUiiZpmLi-waRkGnM4M72fgWdj4QGxqx_-ccCedyixw5jpfeWtniY5RHXQ',
                    